@import './horizontalPriceCalendar/index.less';
@import './sevenDayCalendar/index.less';
@import './flightAndBrand/index.less';
@import '../../less/mediaMixin.less';

// 航班选择页面样式
.fo {
  min-height: 100vh;

  &-banner {
    width: 100%;
    height: 380px;
    position: relative;
    z-index: -1;
    object-fit: cover;

    .screenPad({
      height: 265px;
      });

    .screenMobile({
      height: 130px;
    });
  }

  &-container {
    max-width: 1380px;
    margin: -80px auto 136px;

    .screenPad({
       padding: 0 20px;
      });

    .screenMobile({
      padding: 0 15px;
    });

    .wrap-horizontal-price-calendar {
      margin-top: 30px;
    }

    .wrap-seven-day-calendar {
      margin-top: 30px;
    }

    .wrap-flight-and-brand {
      margin-top: 30px;
    }
  }
}
