<div class="horizontal-price-calendar">
  <!-- left -->
  <div class="horizontal-price-calendar-left">
    <button
      class="__main-button horizontal-price-calendar-lr_btn"
      onclick="goHorizontalPriceCalendarPrev()"
    >
      <img
        class="horizontal-price-calendar-lr_btn-img"
        src="../../../images/flightOptions/left.png"
        alt=""
      />
    </button>

    <div class="horizontal-price-calendar-left-content">
      <!-- PC端显示7个元素 -->
      <% [...new Array(7)].forEach((_, index) => { %>
      <div
        class="horizontal-price-calendar-wrap_item horizontal-price-calendar-wrap_item-pc <%= index === 0? 'horizontal-price-calendar-wrap_item-no_bl' : '' %>"
      >
        <li
          id="horizontal-price-calendar-item-<%= index + 1 %>"
          class="horizontal-price-calendar-item"
        >
          <div>
            <span class="horizontal-price-calendar-item-unit">CNY</span>
            <span class="horizontal-price-calendar-item-amount">1920xxx</span>
          </div>
          <div>
            <span class="horizontal-price-calendar-item-date">06-15xx</span>
            <span class="horizontal-price-calendar-item-week">Sunxx</span>
          </div>
        </li>
      </div>
      <% }) %>

      <!-- Pad端和移动端显示3个元素 -->
      <% [...new Array(3)].forEach((_, index) => { %>
      <div
        class="horizontal-price-calendar-wrap_item horizontal-price-calendar-wrap_item-mobile <%= index === 0? 'horizontal-price-calendar-wrap_item-no_bl' : '' %>"
      >
        <li
          id="horizontal-price-calendar-item-mobile-<%= index + 1 %>"
          class="horizontal-price-calendar-item"
        >
          <div>
            <span class="horizontal-price-calendar-item-unit">CNY</span>
            <span class="horizontal-price-calendar-item-amount">1920xxx</span>
          </div>
          <div>
            <span class="horizontal-price-calendar-item-date">06-15xx</span>
            <span class="horizontal-price-calendar-item-week">Sunxx</span>
          </div>
        </li>
      </div>
      <% }) %>
    </div>

    <button
      class="__main-button horizontal-price-calendar-lr_btn"
      onclick="goHorizontalPriceCalendarNext()"
    >
      <img
        class="horizontal-price-calendar-lr_btn-img"
        src="../../../images/flightOptions/right.png"
        alt=""
      />
    </button>
  </div>

  <!-- right -->
  <button class="__text-button">
    <div class="horizontal-price-calendar-right">
      <img
        class="horizontal-price-calendar-right-img"
        src="../../../images/flightOptions/calendar.png"
        alt=""
      />

      <span class="horizontal-price-calendar-right-text">Price calendar</span>
    </div>
  </button>
</div>
