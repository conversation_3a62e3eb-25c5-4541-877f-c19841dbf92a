.flight-overview {
  display: flex;

  &-left {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    .flight-card {
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      color: white;
      padding: 24px;
      position: relative;

      .flight-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        .trip-type {
          font-size: 16px;
          font-weight: 500;
        }

        .class-type {
          font-size: 16px;
          font-weight: 500;
        }
      }

      .flight-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24px;

        .departure,
        .arrival {
          flex: 1;

          .time {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 4px;
          }

          .date {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
          }

          .airport {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 12px;
          }

          .flight-details {
            font-size: 12px;
            opacity: 0.8;

            span {
              margin-right: 8px;
            }
          }

          .carrier {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 4px;
          }

          .service-info {
            font-size: 12px;
            opacity: 0.8;

            span {
              margin-right: 8px;
            }
          }
        }

        .flight-duration {
          flex: 0 0 200px;
          text-align: center;

          .duration {
            font-size: 14px;
            margin-bottom: 12px;
            opacity: 0.9;
          }

          .flight-line {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            .line {
              width: 120px;
              height: 2px;
              background: rgba(255, 255, 255, 0.6);
              position: relative;

              &::before {
                content: '';
                position: absolute;
                left: 0;
                top: -2px;
                width: 6px;
                height: 6px;
                background: white;
                border-radius: 50%;
              }

              &::after {
                content: '';
                position: absolute;
                right: 0;
                top: -2px;
                width: 6px;
                height: 6px;
                background: white;
                border-radius: 50%;
              }
            }

            .plane-icon {
              position: absolute;
              font-size: 16px;
              background: rgba(255, 255, 255, 0.2);
              border-radius: 50%;
              width: 32px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
      }

      .additional-info {
        display: flex;
        justify-content: space-around;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 16px;

        .info-item {
          text-align: center;

          .label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 4px;
          }

          .value {
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }

    .notes-section {
      padding: 24px;
      background: #fff9e6;
      border-left: 4px solid #ffc107;

      h3 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #333;
      }

      ol {
        margin: 0;
        padding-left: 20px;

        li {
          font-size: 14px;
          line-height: 1.6;
          color: #666;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .policy-section {
      padding: 24px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #dc3545;
      }

      .policy-tables {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
        margin-bottom: 20px;

        .policy-table {
          h4 {
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 16px;
            color: #333;
          }

          .policy-group {
            margin-bottom: 16px;

            h5 {
              font-size: 12px;
              font-weight: 600;
              margin-bottom: 8px;
              color: #666;
            }

            .policy-row {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 0;
              font-size: 12px;
              border-bottom: 1px solid #f0f0f0;

              &:last-child {
                border-bottom: none;
              }

              .price {
                color: #dc3545;
                font-weight: 500;
              }

              .free {
                color: #28a745;
                font-weight: 500;
              }
            }
          }
        }
      }

      .policy-notes {
        font-size: 12px;
        color: #666;
        line-height: 1.5;

        p {
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .action-buttons {
      padding: 24px;
      display: flex;
      gap: 16px;
      background: #f8f9fa;

      button {
        flex: 1;
        height: 48px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &.btn-reselect {
          background: white;
          color: #dc3545;
          border: 2px solid #dc3545;

          &:hover {
            background: #dc3545;
            color: white;
          }
        }

        &.btn-booking {
          background: #dc3545;
          color: white;

          &:hover {
            background: #c82333;
          }
        }
      }
    }
  }

  &-right {
    .ticket {
      width: 360px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      .header {
        padding: 24px 20px 16px;
        font-size: 18px;
        font-weight: 600;
        color: #111;
        text-align: center;
      }
      .passenger {
        padding: 18px 20px 20px;
        border-top: 1px solid #e5e5e5;
        .type {
          font-size: 16px;
          font-weight: 600;
          color: #111;
          margin-bottom: 10px;
        }
        .row {
          font-size: 15px;
          color: #333;
          line-height: 22px;
          &.bold {
            font-weight: 600;
            color: #111;
            margin-top: 8px;
          }
        }
      }
    }
  }
}
