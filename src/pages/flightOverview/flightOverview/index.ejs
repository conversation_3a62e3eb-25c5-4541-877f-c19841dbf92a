<div class="flight-overview">
  <div class="flight-overview-left">
    <!-- 航班信息卡片 -->
    <div class="flight-card">
      <div class="flight-header">
        <span class="trip-type"
          ><%= data.flightOverview.flightData.tripType %></span
        >
        <span class="class-type"
          ><%= data.flightOverview.flightData.classType %></span
        >
      </div>

      <div class="flight-info">
        <div class="departure">
          <div class="time">
            <%= data.flightOverview.flightData.departure.time %>
          </div>
          <div class="date">
            <%= data.flightOverview.flightData.departure.date %>
          </div>
          <div class="airport">
            <%= data.flightOverview.flightData.departure.airport %>
          </div>
          <div class="flight-details">
            <span class="flight-number"
              >🛫 <%= data.flightOverview.flightData.flightNumber %></span
            >
            <span class="aircraft"
              ><%= data.flightOverview.flightData.aircraft %></span
            >
            <span class="shared"
              ><%= data.flightOverview.flightData.shared %></span
            >
          </div>
          <div class="carrier">
            Actual Carrier: <%= data.flightOverview.flightData.carrier %>
          </div>
        </div>

        <div class="flight-duration">
          <div class="duration">
            <%= data.flightOverview.flightData.duration %>
          </div>
          <div class="flight-line">
            <div class="line"></div>
            <div class="plane-icon">✈️</div>
          </div>
        </div>

        <div class="arrival">
          <div class="time">
            <%= data.flightOverview.flightData.arrival.time %>
          </div>
          <div class="date">
            <%= data.flightOverview.flightData.arrival.date %>
          </div>
          <div class="airport">
            <%= data.flightOverview.flightData.arrival.airport %>
          </div>
          <div class="service-info">
            <span class="meal"
              >🍽️ <%= data.flightOverview.flightData.meal %></span
            >
            <span class="class"
              ><%= data.flightOverview.flightData.businessClass %></span
            >
          </div>
        </div>
      </div>

      <!-- 行李、里程信息 -->
      <div class="additional-info">
        <% data.flightOverview.additionalInfo.forEach(function(info) { %>
        <div class="info-item">
          <div class="label"><%= info.label %></div>
          <div class="value"><%= info.value %></div>
        </div>
        <% }); %>
      </div>
    </div>

    <!-- 注意事项 -->
    <div class="notes-section">
      <h3>Notes:</h3>
      <ol>
        <% data.flightOverview.notes.forEach(function(note) { %>
        <li><%= note %></li>
        <% }); %>
      </ol>
    </div>

    <!-- 退改签政策 -->
    <div class="policy-section">
      <h3>Changes/Cancellations:</h3>

      <div class="policy-tables">
        <% data.flightOverview.policyTables.forEach(function(table) { %>
        <div class="policy-table">
          <h4><%= table.title %></h4>

          <% table.groups.forEach(function(group) { %>
          <div class="policy-group">
            <h5><%= group.title %></h5>
            <% group.items.forEach(function(item) { %>
            <div class="policy-row">
              <span><%= item.type %></span>
              <span class="<%= item.priceClass %>"><%= item.price %></span>
            </div>
            <% }); %>
          </div>
          <% }); %>
        </div>
        <% }); %>
      </div>

      <div class="policy-notes">
        <% data.flightOverview.policyNotes.forEach(function(note) { %>
        <p><%= note %></p>
        <% }); %>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="action-buttons">
      <button class="btn-reselect" onclick="handleReselect()">Reselect</button>
      <button class="btn-booking" onclick="handleBooking()">Booking</button>
    </div>
  </div>

  <div class="flight-overview-right">
    <div class="ticket">
      <div class="header">Total price: CNY 2018</div>
      <% data.flightOverview.passengers.forEach(p => { %>
      <div class="passenger">
        <div class="type"><%= p.type %></div>
        <div class="row">Fare: CNY <%= p.fare %></div>
        <div class="row">Taxes: CNY <%= p.taxes %></div>
        <div class="row bold">
          Total: CNY <%= (p.fare + p.taxes).toFixed(1) %>
        </div>
      </div>
      <% }) %>
    </div>
  </div>
</div>
